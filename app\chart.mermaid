graph TD
    %% RSS Download Phase
    A[🚀 Start: RSS Download] --> B{Check isRunning.txt}
    B -->|Already Running| C[⏸️ Skip Execution]
    B -->|Not Running| D[📡 Fetch RSS Feed from nyaa.si]
    D --> E{RSS Fetch Success?}
    E -->|Failed| F[🔄 Retry up to 3 times]
    F -->|Max Retries| G[❌ Exit with Error]
    F -->|Retry| D
    E -->|Success| H[📋 Parse RSS XML]
    H --> I[🔍 Filter by Whitelist]
    I --> J{ToonsHub Format?}
    J -->|Yes| K{H.265 Format?}
    K -->|Yes| L[⏭️ Skip H.265 Release]
    K -->|No H.264| M[📥 Download Torrent]
    J -->|No| M
    M --> N[📊 Progress Tracking]
    N --> O[🏷️ File Renaming]
    O --> P[📝 Update Processed Lists]
    P --> Q[🧹 Start Clear Process]

    %% Clear Phase
    Q --> R[🎬 Clear.js: Extract Subtitles]
    R --> S[📁 Scan Downloads for .mkv]
    S --> T{MKV Files Found?}
    T -->|No| U[⚠️ No Files to Process]
    T -->|Yes| V[🔄 Process Each File]
    V --> W[🎯 Extract English Subtitles]
    W --> X[🇫🇷 Extract French Subtitles]
    X --> Y{French Extraction Success?}
    Y -->|Failed| Z[⚠️ Continue without French]
    Y -->|Success| AA[✅ Dual Language Ready]
    Z --> BB[🧹 Clean Dialogue Lines]
    AA --> BB
    BB --> CC[👥 Actor Prediction]
    CC --> DD[💾 Save to extracted/]
    DD --> EE[🔄 Next File or Continue]
    EE --> FF[🌟 Start Translation Process]

    %% Translation Phase
    FF --> GG[🤖 Claude4 Translator Init]
    GG --> HH[📊 Load Configuration]
    HH --> II[🎭 Fetch Anime Metadata]
    II --> JJ{Metadata Success?}
    JJ -->|Failed| KK[⚠️ Continue without Metadata]
    JJ -->|Success| LL[📚 Initialize Context Manager]
    KK --> MM[🎬 Scene Detection]
    LL --> MM
    MM --> NN[🔍 Analyze Scene Boundaries]
    NN --> OO[🎯 Process Each Scene]
    OO --> PP{Screenshot Needed?}
    PP -->|Yes| QQ[📸 Capture Frame]
    PP -->|No| RR[🔧 Translate with Tools]
    QQ --> RR
    RR --> SS[🛠️ Tool: translate_with_context]
    SS --> TT{Quality Assessment}
    TT -->|Poor Quality| UU[🔍 Second Language Validator]
    TT -->|Good Quality| VV[📚 Examples Reference]
    UU --> WW[🇫🇷 Cross-validate with French]
    WW --> XX{Validation Improves?}
    XX -->|Yes| YY[✨ Apply Improved Translation]
    XX -->|No| ZZ[⚠️ Flag for Review]
    VV --> AAA[🔍 Pattern Matching]
    AAA --> BBB[📖 Find Relevant Examples]
    BBB --> YY
    YY --> CCC[🇵🇱 Polish Grammar Correction]
    ZZ --> CCC
    CCC --> DDD{High Priority Punctuation?}
    DDD -->|Yes| EEE[✅ Auto-apply Fixes]
    DDD -->|No| FFF[📝 Suggest Corrections]
    EEE --> GGG[💾 Update Context Manager]
    FFF --> GGG
    GGG --> HHH[📊 Metadata Persistence]
    HHH --> III{More Scenes?}
    III -->|Yes| OO
    III -->|No| JJJ[💾 Save Episode Data]
    JJJ --> KKK[🔄 Next File or Continue]
    KKK --> LLL[🎯 Start Replace Process]

    %% Replace Phase
    LLL --> MMM[🎭 Separate Actors]
    MMM --> NNN[📁 Process withActors files]
    NNN --> OOO[✂️ Remove Actor Prefixes]
    OOO --> PPP[💾 Save to clean/]
    PPP --> QQQ[🔄 Apply Translation]
    QQQ --> RRR[📁 Match .ass with .txt]
    RRR --> SSS{Files Match?}
    SSS -->|No| TTT[⚠️ Skip Unmatched]
    SSS -->|Yes| UUU[🔄 Replace Dialogue Lines]
    UUU --> VVV[⏰ Preserve Timing]
    VVV --> WWW[👥 Preserve Actors]
    WWW --> XXX[💾 Save Final .ass]
    XXX --> YYY[🧹 Cleanup Directories]
    YYY --> ZZZ[✅ Process Complete]

    %% Utility Scripts
    AAAA[🎵 Karaoke Normalize] -.-> BBBB[⏰ Normalize Timecodes]
    CCCC[🎵 Karaoke Offset] -.-> DDDD[⏰ Offset Timecodes]
    EEEE[🧪 Production Test] -.-> FFFF[🔬 Test All Components]

    %% Error Handling
    GGGG[❌ Error Occurred] --> HHHH{Critical Error?}
    HHHH -->|Yes| IIII[🛑 Exit Process]
    HHHH -->|No| JJJJ[📝 Log Error]
    JJJJ --> KKKK[🔄 Continue Processing]

    %% Logging System
    LLLL[📊 Logging System] --> MMMM[📄 info.log]
    LLLL --> NNNN[❌ error.log]
    LLLL --> OOOO[💥 exception.log]
    LLLL --> PPPP[🚫 rejection.log]

    %% State Management
    QQQQ[📁 State Files] --> RRRR[🏃 isRunning.txt]
    QQQQ --> SSSS[🆔 lastId.txt]
    QQQQ --> TTTT[📋 processedTitles.txt]
    QQQQ --> UUUU[🔢 processed_ids.txt]

    %% Configuration
    VVVV[⚙️ Configuration] --> WWWW[🔧 config.js]
    VVVV --> XXXX[📋 whitelist.txt]
    VVVV --> YYYY[📋 whitelist_other.txt]
    VVVV --> ZZZZ[📚 examples.xml]

    %% Styling
    classDef startEnd fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef process fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef error fill:#ffebee,stroke:#b71c1c,stroke-width:2px
    classDef success fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef tool fill:#e3f2fd,stroke:#0d47a1,stroke-width:2px

    class A,ZZZ startEnd
    class D,H,I,M,R,W,X,GG,MM,RR,MMM,QQQ process
    class B,E,J,K,T,Y,JJ,PP,TT,XX,DDD,III,SSS,HHHH decision
    class G,U,IIII error
    class YY,EEE,XXX success
    class SS,UU,VV,CCC tool